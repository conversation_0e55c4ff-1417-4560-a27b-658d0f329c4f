import subprocess
import threading
import time
import re
import json


from fabric.utils import idle_add
from fabric.widgets.datetime import DateTime
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.label import Label
from fabric.widgets.overlay import Overlay
from fabric.widgets.button import Button
from fabric.widgets.box import Box
from fabric.widgets.eventbox import EventBox
from fabric.widgets.scale import Scale
from fabric.widgets.svg import Svg
from fabric.widgets.stack import Stack
from fabric.widgets.revealer import Revealer
from widgets.wayland import WaylandWindow as Window
from fabric.widgets.scrolledwindow import ScrolledWindow
from fabric.utils.helpers import (
    exec_shell_command_async,
    get_relative_path,
    bulk_connect,
    exec_shell_command,
)
from fabric.audio import Audio
from gi.repository import GLib, Gtk
from fabric import Fabricator
from .bluetooth import BluetoothConnections
from services.brightness import Brightness

from utils.service import modus_service
from utils.exml import exml
import utils.icons as icons

audio_service = Audio()
brightness_service = Brightness.get_initial()


class ModusControlCenter(Window):
    def __init__(self, **kwargs):
        super().__init__(
            layer="top",
            title="modus",
            anchor="top right",
            margin="2px 10px 0px 0px",
            exclusivity="auto",
            keyboard_mode="on-demand",
            name="control-center-menu",
            visible=False,
            **kwargs,
        )
        self.focus_mode = False

        self.add_keybinding("Escape", self.hide_controlcenter)

        volume = 100
        wlan = modus_service.sc("wlan-changed", self.wlan_changed)
        bluetooth = modus_service.sc("bluetooth-changed", self.bluetooth_changed)
        music = modus_service.sc("music-changed", self.audio_changed)

        audio_service.connect("changed", self.audio_changed)
        audio_service.connect("changed", self.volume_changed)

        self.wlan_label = Label(wlan, name="wifi-widget-label", h_align="start")
        self.bluetooth_label = Label(
            bluetooth, name="bluetooth-widget-label", h_align="start"
        )
        self.volume_icon = Label("", name="volume-widget-icon", h_align="start")
        self.volume_scale = Scale(
            value=volume,
            min_value=0,
            max_value=100,
            increments=(5, 5),
            name="volume-widget-slider",
            size=30,
            h_expand=True,
        )
        self.volume_scale.connect("change-value", self.set_volume)
        self.volume_scale.connect("scroll-event", self.on_volume_scroll)

        # Create brightness scale
        current_brightness = brightness_service.screen_brightness
        brightness_percentage = int((current_brightness / brightness_service.max_screen) * 100) if brightness_service.max_screen > 0 else 50

        self.brightness_scale = Scale(
            value=brightness_percentage,
            min_value=0,
            max_value=100,
            increments=(5, 5),
            name="brightness-widget-slider",
            size=30,
            h_expand=True,
        )

        # Only connect brightness controls if brightness service is available
        if brightness_service.max_screen > 0:
            self.brightness_scale.connect("change-value", self.set_brightness)
            self.brightness_scale.connect("scroll-event", self.on_brightness_scroll)
            brightness_service.connect("screen", self.brightness_changed)
        else:
            # Disable brightness scale if no backlight device available
            self.brightness_scale.set_sensitive(False)

        # Create brightness icon
        self.brightness_icon = Svg(
            svg_file=get_relative_path("../../config/assets/icons/brightness.svg"),
            style_classes="icon",
            name="brightness-icon",
        )

        self.music_widget = Box(
            name="music-widget",
            # children=[Player()],
        )

        self.wifi_man = Box()
        self.bluetooth_man = BluetoothConnections(self)

        self.has_bluetooth_open = False
        self.has_wifi_open = False

        self.bluetooth_svg = Svg(
            svg_file=get_relative_path(
                "../../assets/svgs/bluetooth.svg"
                if bluetooth == "On"
                else "../../config/assets/icons/bluetooth-off.svg"
            ),
            style_classes="icon",
        )
        self.wifi_svg = (
            Svg(
                svg_file=get_relative_path("../../config/assets/icons/wifi.svg"),
                style_classes="icon",
            )
            if wlan != "No Connection"
            else Svg(
                svg_file=get_relative_path("../../config/assets/icons/wifi-off.svg"),
                style_classes="icon",
            )
        )

        self.bluetooth_widget = Button(
            name="bluetooth-widget",
            child=Box(
                orientation="h",
                children=[
                    self.bluetooth_svg,
                    Box(
                        name="bluetooth-widget-info",
                        orientation="vertical",
                        children=[
                            Label(
                                label="Bluetooth",
                                style_classes="title ct",
                                h_align="start",
                            ),
                            self.bluetooth_label,
                        ],
                    ),
                ],
            ),
            on_clicked=self.open_bluetooth,
        )

        self.wlan_widget = Button(
            name="wifi-widget",
            child=Box(
                orientation="h",
                children=[
                    self.wifi_svg,
                    Box(
                        name="wifi-widget-info",
                        orientation="vertical",
                        children=[
                            Label(
                                label="Wi-Fi", style_classes="title ct", h_align="start"
                            ),
                            self.wlan_label,
                        ],
                    ),
                ],
            ),
            on_clicked=self.open_wifi,
        )

        self.focus_icon = Svg(
            svg_file=get_relative_path("../../config/assets/icons/dnd-off.svg"),
            style_classes="icon",
        )

        self.focus_widget = Button(
            name="focus-widget",
            child=Box(
                orientation="h",
                children=[
                    self.focus_icon,
                    Label(label="Focus", style_classes="title ct", h_align="start"),
                ],
            ),
            on_clicked=self.set_dont_disturb,
        )

        self.widgets = exml(
            file=get_relative_path("controlcenter.xml"),
            root=Box,
            tags={
                "Box": Box,
                "Button": Button,
                "Label": Label,
                "Scale": Scale,
                "Svg": Svg,
            },
            refs={
                "self.wlan_label": self.wlan_label,
                "self.bluetooth_label": self.bluetooth_label,
                "self.volume_scale": self.volume_scale,
                "self.brightness_scale": self.brightness_scale,
                "self.brightness_icon": self.brightness_icon,
                "self.music_widget": self.music_widget,
                "self.volume_icon": self.volume_icon,
                "self.bluetooth_widget": self.bluetooth_widget,
                "self.wlan_widget": self.wlan_widget,
                "self.focus_widget": self.focus_widget,
            },
        )

        self.bluetooth_widgets = exml(
            file=get_relative_path("bluetooth.xml"),
            root=Box,
            tags={
                "Box": Box,
                "Button": Button,
                "Label": Label,
                "Scale": Scale,
                "Svg": Svg,
            },
            refs={"self.bluetooth_man": self.bluetooth_man},
        )

        self.wifi_widgets = exml(
            file=get_relative_path("wifi.xml"),
            root=Box,
            tags={
                "Box": Box,
                "Button": Button,
                "Label": Label,
                "Scale": Scale,
                "Svg": Svg,
            },
            refs={"self.wifi_man": self.wifi_man},
        )

        self.center_box = CenterBox(start_children=[self.widgets])

        self.bluetooth_center_box = CenterBox(start_children=[self.bluetooth_widgets])

        self.wifi_center_box = CenterBox(start_children=[self.wifi_widgets])

        self.widgets.set_size_request(300, -1)
        self.bluetooth_center_box.set_size_request(300, -1)
        self.wifi_center_box.set_size_request(300, -1)

        self.children = self.center_box

    def set_dont_disturb(self, *_):
        self.focus_mode = not self.focus_mode
        modus_service.dont_disturb = self.focus_mode
        self.focus_icon.set_from_file(
            get_relative_path(
                "../../config/assets/icons/dnd.svg"
                if self.focus_mode
                else "../../config/assets/icons/dnd-off.svg"
            )
        )

    def set_volume(self, _, __, volume):
        audio_service.speaker.volume = round(volume)

    def set_brightness(self, _, __, brightness):
        # Convert percentage to actual brightness value
        brightness_value = int((brightness / 100) * brightness_service.max_screen)
        brightness_service.screen_brightness = brightness_value

    def brightness_changed(self, _, brightness_value):
        # Convert raw brightness value to percentage (following OSD pattern)
        if brightness_service.max_screen > 0:
            brightness_percentage = int((brightness_value / brightness_service.max_screen) * 100)

            # Update the brightness scale when brightness changes externally
            GLib.idle_add(
                lambda: self.brightness_scale.set_value(brightness_percentage)
            )

    def on_volume_scroll(self, widget, event):
        # Handle mouse scroll events on volume slider
        current_value = self.volume_scale.get_value()
        scroll_step = 5  # Adjust volume by 5% per scroll step

        # Check scroll direction
        if event.direction == 0:  # Scroll up
            new_value = min(100, current_value + scroll_step)
        elif event.direction == 1:  # Scroll down
            new_value = max(0, current_value - scroll_step)
        else:
            return False

        self.volume_scale.set_value(new_value)
        return True  # Event handled

    def on_brightness_scroll(self, widget, event):
        # Handle mouse scroll events on brightness slider
        current_value = self.brightness_scale.get_value()
        scroll_step = 5  # Adjust brightness by 5% per scroll step

        # Check scroll direction
        if event.direction == 0:  # Scroll up
            new_value = min(100, current_value + scroll_step)
        elif event.direction == 1:  # Scroll down
            new_value = max(0, current_value - scroll_step)
        else:
            return False

        self.brightness_scale.set_value(new_value)
        return True  # Event handled

    def set_children(self, children):
        self.children = children

    def open_bluetooth(self, *_):
        idle_add(lambda *_: self.set_children(self.bluetooth_center_box))
        self.has_bluetooth_open = True

    def open_wifi(self, *_):
        idle_add(lambda *_: self.set_children(self.wifi_center_box))
        self.has_wifi_open = True

    def close_bluetooth(self, *_):
        idle_add(lambda *_: self.set_children(self.center_box))
        self.has_bluetooth_open = False

    def close_wifi(self, *_):
        idle_add(lambda *_: self.set_children(self.center_box))
        self.has_wifi_open = False

    def _set_mousecapture(self, visible: bool):
        self.set_visible(visible)
        if not visible:
            self.close_bluetooth()

    def volume_changed(
        self,
        _,
    ):
        GLib.idle_add(
            lambda: self.volume_scale.set_value(int(audio_service.speaker.volume))
        )  # type: ignore

    def wlan_changed(self, _, wlan):
        self.wifi_svg.set_from_file(
            get_relative_path(
                "../../config/assets/icons/wifi.svg"
                if wlan != "No Connection"
                else "../../config/assets/icons/wifi-off.svg"
            )
        )
        GLib.idle_add(lambda: self.wlan_label.set_property("label", wlan))

    def bluetooth_changed(self, _, bluetooth):
        self.bluetooth_svg.set_from_file(
            get_relative_path(
                "../../config/assets/icons/bluetooth.svg"
                if bluetooth == "On"
                else "../../config/assets/icons/bluetooth-off.svg"
            )
        )
        GLib.idle_add(lambda: self.bluetooth_label.set_property("label", bluetooth))

    def audio_changed(self, *_):
        pass

    def _init_mousecapture(self, mousecapture):
        self._mousecapture_parent = mousecapture

    def hide_controlcenter(self, *_):
        self._mousecapture_parent.toggle_mousecapture()
        self.set_visible(False)
