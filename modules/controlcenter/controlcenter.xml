<esh name="ControlCenter">
    <BoxV h_expand="true" name="control-center-widgets">
        <BoxH name="top-widget" h_expand="true">
            <BoxV name="wb-widget" style_classes="menu" spacing="5">
                <Ref>self.wlan_widget</Ref>
                <Ref>self.bluetooth_widget</Ref>
            </BoxV>
            <BoxH name="dnd-widget" style_classes="menu" h_expand="true">
                <Ref>self.focus_widget</Ref>
            </BoxH>
        </BoxH>
        <BoxV name="brightness-widget" style_classes="menu" h_expand="true">
            <Label label="Display" style_classes="title" h_align="start"/>
            <Ref>self.brightness_scale</Ref>
            <Label label="󰖨 " name="brightness-widget-icon" h_align="start"/>
        </BoxV>
        <BoxV name="volume-widget" style_classes="menu">
            <Label label="Sound" style_classes="title" h_align="start"/>
            <Ref>self.volume_scale</Ref>
            <Ref>self.volume_icon</Ref>
        </BoxV>
        <BoxV>
            <Ref>self.music_widget</Ref>
        </BoxV>
    </BoxV>
</esh>
